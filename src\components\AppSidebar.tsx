import { 
  Building2, 
  Users, 
  MapPin,
  Gift,
  Shield,
  UserCheck,
  Phone,
  ChevronRight,
  School,
  GraduationCap,
  ShieldCheck,
  HeartHandshake,
  Home,
  Car,
  Plane,
  Clock,
  CalendarDays,
  Calendar,
  DollarSign,
  LogOut,
  UserPlus,
  Briefcase,
  TrendingUp,
  Medal,
  ArrowUp,
  MapIcon,
  Building,
  Hotel,
  Gamepad2,
  TreePine,
  Target,
  Trophy,
  Dumbbell,
  Leaf,
  UserCog,
  FileCheck,
  Bed,
  Bus,
  Activity,
  KeyRound
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

const navigationItems = [
  {
    title: "Our Company",
    icon: Building2,
    items: [
      { title: "Shareholders", url: "/company/shareholders", icon: Users },
      { title: "Vision & Mission", url: "/company/vision-mission", icon: Target },
      { title: "Core Values", url: "/company/core-values", icon: HeartHandshake },
      { title: "Sister Companies", url: "/company/sister-companies", icon: Building },
      { title: "CEO", url: "/company/ceo", icon: UserCog },
      { title: "Executive Chart", url: "/company/executive-chart", icon: TrendingUp },
    ]
  },
  {
    title: "Our Destination",
    icon: MapPin,
    items: [
      { title: "Prime Location", url: "/destination/location", icon: MapIcon },
      { title: "Residentials", url: "/destination/residentials", icon: Home },
      { title: "Hotels", url: "/destination/hotels", icon: Hotel },
      { 
        title: "Sports", 
        url: "/destination/sports", 
        icon: Gamepad2,
        subItems: [
          { title: "Golf", url: "/destination/sports/golf", icon: Target },
          { title: "Tennis Academy", url: "/destination/sports/tennis", icon: Trophy },
          { title: "Club House", url: "/destination/sports/clubhouse", icon: Dumbbell },
        ]
      },
      { title: "Sustainability", url: "/destination/sustainability", icon: Leaf },
    ]
  },
  {
    title: "Our Benefits",
    icon: Gift,
    items: [
      { 
        title: "Education", 
        url: "/benefits/education", 
        icon: School,
        subItems: [
          { title: "School", url: "/benefits/education#school", icon: School },
          { title: "University", url: "/benefits/education#university", icon: GraduationCap },
        ]
      },
      { 
        title: "Insurance", 
        url: "/benefits/insurance", 
        icon: ShieldCheck,
        subItems: [
          { title: "Medical", url: "/benefits/insurance#medical", icon: HeartHandshake },
          { title: "Life", url: "/benefits/insurance#life", icon: Shield },
          { title: "Social", url: "/benefits/insurance#social", icon: Users },
        ]
      },
      { 
        title: "Accommodation", 
        url: "/benefits/accommodation", 
        icon: Home,
        subItems: [
          { title: "Lodging", url: "/benefits/accommodation#lodging", icon: Bed },
          { title: "Meals", url: "/benefits/accommodation#meals", icon: Users },
          { title: "Laundry", url: "/benefits/accommodation#laundry", icon: Home },
        ]
      },
      { 
        title: "Commuting", 
        url: "/benefits/commuting", 
        icon: Car,
        subItems: [
          { title: "CAI-SOMA", url: "/benefits/commuting#cai-soma", icon: Bus },
          { title: "SOMA-Hurghada", url: "/benefits/commuting#soma-hurghada", icon: Car },
          { title: "Home-Office-Home", url: "/benefits/commuting#home-office", icon: Home },
        ]
      },
      { 
        title: "Sports & Activities", 
        url: "/benefits/sports", 
        icon: Activity,
        subItems: [
          { title: "Club House", url: "/benefits/sports#clubhouse", icon: Dumbbell },
          { title: "GYM", url: "/benefits/sports#gym", icon: Activity },
          { title: "Staff Rates", url: "/benefits/sports#rates", icon: DollarSign },
        ]
      },
      { title: "Staff Home Owners", url: "/benefits/homeowners", icon: KeyRound },
    ]
  },
  {
    title: "Policies",
    icon: Shield,
    items: [
      { 
        title: "Talent Management", 
        url: "/policies/talent-management", 
        icon: UserPlus,
        subItems: [
          { title: "Hiring", url: "/policies/talent-management#hiring", icon: UserPlus },
          { title: "L&D", url: "/policies/talent-management#learning", icon: School },
          { title: "Performance", url: "/policies/talent-management#performance", icon: TrendingUp },
          { title: "Succession", url: "/policies/talent-management#succession", icon: ArrowUp },
          { title: "Promotion", url: "/policies/talent-management#promotion", icon: Medal },
        ]
      },
      { 
        title: "Attendance", 
        url: "/policies/attendance", 
        icon: Clock,
        subItems: [
          { title: "Working Hours", url: "/policies/attendance#hours", icon: Clock },
          { title: "Sign-In & Out", url: "/policies/attendance#signin", icon: FileCheck },
          { title: "Tardiness", url: "/policies/attendance#tardiness", icon: Clock },
          { title: "Permissions", url: "/policies/attendance#permissions", icon: FileCheck },
          { title: "Holidays & Weekends", url: "/policies/attendance#holidays", icon: Calendar },
        ]
      },
      { 
        title: "Leave Management", 
        url: "/policies/leave-management", 
        icon: CalendarDays,
        subItems: [
          { title: "Leave-1", url: "/policies/leave-management#leave1", icon: Calendar },
          { title: "Leave-2", url: "/policies/leave-management#leave2", icon: Calendar },
          { title: "Leave-3", url: "/policies/leave-management#leave3", icon: Calendar },
          { title: "Leave-4", url: "/policies/leave-management#leave4", icon: Calendar },
          { title: "Leave-5", url: "/policies/leave-management#leave5", icon: Calendar },
          { title: "Leave-6", url: "/policies/leave-management#leave6", icon: Calendar },
          { title: "Leave-7", url: "/policies/leave-management#leave7", icon: Calendar },
          { title: "Leave-8", url: "/policies/leave-management#leave8", icon: Calendar },
        ]
      },
      { 
        title: "Payroll Management", 
        url: "/policies/payroll", 
        icon: DollarSign,
        subItems: [
          { title: "Payroll Protocol", url: "/policies/payroll#protocol", icon: DollarSign },
          { title: "Salary Increase", url: "/policies/payroll#increase", icon: TrendingUp },
          { title: "Performance Bonus", url: "/policies/payroll#bonus", icon: Medal },
        ]
      },
      { 
        title: "Exit Management", 
        url: "/policies/exit-management", 
        icon: LogOut,
        subItems: [
          { title: "Resignation", url: "/policies/exit-management#resignation", icon: LogOut },
          { title: "End of Contract", url: "/policies/exit-management#contract", icon: FileCheck },
          { title: "Retirement", url: "/policies/exit-management#retirement", icon: Users },
          { title: "Death", url: "/policies/exit-management#death", icon: HeartHandshake },
          { title: "Legal Dismissal", url: "/policies/exit-management#dismissal", icon: Shield },
        ]
      },
      { 
        title: "Travel", 
        url: "/policies/travel", 
        icon: Plane,
        subItems: [
          { title: "Local", url: "/policies/travel#local", icon: Car },
          { title: "Abroad", url: "/policies/travel#abroad", icon: Plane },
        ]
      },
      { 
        title: "Accommodation", 
        url: "/policies/accommodation", 
        icon: Home,
        subItems: [
          { title: "Housing", url: "/policies/accommodation#housing", icon: Home },
          { title: "Pets", url: "/policies/accommodation#pets", icon: HeartHandshake },
        ]
      },
      { 
        title: "Company Vehicles", 
        url: "/policies/vehicles", 
        icon: Car,
        subItems: [
          { title: "Eligibility", url: "/policies/vehicles#eligibility", icon: FileCheck },
          { title: "Accidents", url: "/policies/vehicles#accidents", icon: Shield },
          { title: "Traffic Violations", url: "/policies/vehicles#violations", icon: Shield },
          { title: "Misuse", url: "/policies/vehicles#misuse", icon: Shield },
        ]
      },
      { 
        title: "Workplace", 
        url: "/policies/workplace", 
        icon: Briefcase,
        subItems: [
          { title: "Dress Code", url: "/policies/workplace#dress", icon: Users },
          { title: "Alcohol & Drugs", url: "/policies/workplace#substances", icon: Shield },
          { title: "Harassment & Violence", url: "/policies/workplace#harassment", icon: Shield },
          { title: "Bullying", url: "/policies/workplace#bullying", icon: Shield },
          { title: "Disclosure", url: "/policies/workplace#disclosure", icon: FileCheck },
          { title: "Discrimination", url: "/policies/workplace#discrimination", icon: Shield },
        ]
      },
    ]
  },
  {
    title: "Our Team",
    icon: UserCheck,
    items: [
      { title: "HR Team", url: "/team/hr", icon: Users },
    ]
  },
  {
    title: "Contacts",
    icon: Phone,
    items: [
      { title: "Important Contacts", url: "/contacts", icon: Phone },
    ]
  }
];

export function AppSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const currentPath = location.pathname;
  const collapsed = state === "collapsed";

  const isActive = (path: string) => currentPath === path || currentPath.startsWith(path + "/");
  const getNavCls = ({ isActive }: { isActive: boolean }) =>
    isActive 
      ? "bg-corporate-light text-corporate-navy font-medium border-r-2 border-corporate-blue" 
      : "text-corporate-gray hover:bg-muted hover:text-corporate-navy transition-colors";

  const isGroupActive = (items: any[]) => {
    return items.some(item => 
      isActive(item.url) || 
      (item.subItems && item.subItems.some((subItem: any) => isActive(subItem.url)))
    );
  };

  return (
    <Sidebar className="border-r border-border bg-card">
      <SidebarContent>
        <div className="p-6 border-b border-border">
          <div className="flex items-center gap-2">
            <Building2 className="h-8 w-8 text-corporate-navy" />
            {!collapsed && (
              <div>
                <h1 className="text-lg font-bold text-corporate-navy">HR Manual</h1>
                <p className="text-sm text-corporate-gray">Employee Handbook</p>
              </div>
            )}
          </div>
        </div>

        <SidebarGroup className="px-4 py-6">
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              <Accordion type="multiple" className="w-full">
                {navigationItems.map((section) => (
                  <AccordionItem key={section.title} value={section.title} className="border-none">
                    <AccordionTrigger 
                      className={`hover:no-underline py-3 px-3 rounded-md transition-colors ${
                        isGroupActive(section.items) 
                          ? "bg-corporate-light text-corporate-navy font-medium" 
                          : "text-corporate-gray hover:bg-muted hover:text-corporate-navy"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <section.icon className="h-5 w-5 shrink-0" />
                        {!collapsed && <span className="truncate">{section.title}</span>}
                      </div>
                    </AccordionTrigger>
                    
                    {!collapsed && (
                      <AccordionContent className="pb-0">
                        <div className="ml-8 space-y-1">
                          {section.items.map((item) => (
                            <div key={item.title}>
                              <SidebarMenuItem>
                                <SidebarMenuButton asChild className="h-10">
                                  <NavLink 
                                    to={item.url} 
                                    className={({ isActive }) => 
                                      `flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-all ${getNavCls({ isActive })}`
                                    }
                                  >
                                    <item.icon className="h-4 w-4 shrink-0" />
                                    <span className="truncate">{item.title}</span>
                                    {item.subItems && <ChevronRight className="h-3 w-3 ml-auto" />}
                                  </NavLink>
                                </SidebarMenuButton>
                              </SidebarMenuItem>
                              
                              {item.subItems && (
                                <div className="ml-6 mt-1 space-y-1">
                                  {item.subItems.map((subItem) => (
                                    <SidebarMenuItem key={subItem.title}>
                                      <SidebarMenuButton asChild className="h-8">
                                        <NavLink 
                                          to={subItem.url} 
                                          className={({ isActive }) => 
                                            `flex items-center gap-3 px-3 py-1.5 rounded-md text-xs transition-all ${getNavCls({ isActive })}`
                                          }
                                        >
                                          <subItem.icon className="h-3 w-3 shrink-0" />
                                          <span className="truncate">{subItem.title}</span>
                                        </NavLink>
                                      </SidebarMenuButton>
                                    </SidebarMenuItem>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    )}
                  </AccordionItem>
                ))}
              </Accordion>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}