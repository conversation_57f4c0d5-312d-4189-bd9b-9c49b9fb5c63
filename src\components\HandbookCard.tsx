import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

interface HandbookCardProps {
  title: string
  description: string
  items: string[]
  image: string
  imageAlt: string
  href: string
  className?: string
}

export function HandbookCard({ 
  title, 
  description, 
  items, 
  image, 
  imageAlt, 
  href,
  className 
}: HandbookCardProps) {
  return (
    <Card className={cn(
      "group overflow-hidden transition-all duration-300 hover:shadow-elevated border-0 bg-gradient-card",
      className
    )}>
      <CardContent className="p-0">
        <div className="flex flex-col lg:flex-row">
          <div className="flex-1 p-8">
            <h3 className="text-2xl font-bold text-corporate-navy mb-3 group-hover:text-corporate-blue transition-colors">
              {title}
            </h3>
            <p className="text-corporate-gray mb-6 leading-relaxed">
              {description}
            </p>
            <ul className="space-y-2 mb-6">
              {items.map((item, index) => (
                <li key={index} className="flex items-start gap-2 text-sm text-foreground">
                  <div className="w-1.5 h-1.5 rounded-full bg-corporate-blue mt-2 shrink-0" />
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <Button 
              variant="ghost" 
              className="group/btn text-corporate-navy hover:text-corporate-blue hover:bg-corporate-light p-0 h-auto font-medium"
            >
              Learn More 
              <ChevronRight className="ml-1 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
            </Button>
          </div>
          <div className="lg:w-80 h-48 lg:h-auto relative overflow-hidden">
            <img 
              src={image} 
              alt={imageAlt}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-card/20 to-transparent" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}