import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Leaf, Sun, Zap, Droplets } from "lucide-react";

const Sustainability = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Sustainability" }
  ];

  return (
    <PageLayout 
      title="Sustainability Initiatives" 
      description="Our commitment to environmental responsibility through solar energy and natural gas solutions."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Solar Energy Program">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Sun className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Solar Panel Installation</h4>
                <p className="text-corporate-gray">
                  Comprehensive solar panel systems installed across all major facilities, significantly reducing our carbon footprint and energy costs.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Zap className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Renewable Energy Goals</h4>
                <p className="text-corporate-gray">
                  Target of 70% renewable energy usage by 2025, with continuous expansion of solar capacity across the development.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Natural Gas Infrastructure">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Leaf className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Clean Energy Transition</h4>
                <p className="text-corporate-gray">
                  Natural gas infrastructure provides cleaner energy for heating, cooking, and backup power generation throughout the community.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Droplets className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Emission Reduction</h4>
                <p className="text-corporate-gray">
                  Natural gas usage has reduced our CO2 emissions by 30% compared to traditional energy sources.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Environmental Impact">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-6 rounded-lg bg-green-50 border border-green-200">
            <div className="text-3xl font-bold text-green-600 mb-2">70%</div>
            <p className="text-sm text-green-700">Renewable Energy Target</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-blue-50 border border-blue-200">
            <div className="text-3xl font-bold text-blue-600 mb-2">30%</div>
            <p className="text-sm text-blue-700">CO2 Reduction</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-yellow-50 border border-yellow-200">
            <div className="text-3xl font-bold text-yellow-600 mb-2">100%</div>
            <p className="text-sm text-yellow-700">Coverage Goal</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-purple-50 border border-purple-200">
            <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
            <p className="text-sm text-purple-700">Clean Energy Monitoring</p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Future Initiatives">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 rounded-lg bg-gradient-primary text-white">
            <h4 className="text-xl font-semibold mb-4">Upcoming Projects</h4>
            <ul className="space-y-2 text-white/90">
              <li>• Energy storage battery systems</li>
              <li>• Smart grid implementation</li>
              <li>• Electric vehicle charging stations</li>
              <li>• Water recycling systems</li>
              <li>• Waste-to-energy programs</li>
            </ul>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="text-xl font-semibold text-corporate-navy mb-4">Sustainability Goals</h4>
            <ul className="space-y-2 text-corporate-gray">
              <li>• Carbon neutral operations by 2030</li>
              <li>• Zero waste to landfill by 2028</li>
              <li>• 50% water consumption reduction</li>
              <li>• LEED certification for new buildings</li>
              <li>• Biodiversity conservation programs</li>
            </ul>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Sustainability;
