import React, { useState } from "react";
import UserLogin from "./UserLogin";
import { Card } from "../components/ui/card";

export default function PrivatePage() {
  const [user, setUser] = useState<string | null>(null);

  if (!user) {
    return <UserLogin onLogin={setUser} />;
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Card className="p-8 w-full max-w-lg">
        <h1 className="text-2xl font-bold mb-4">Welcome, {user}!</h1>
        <p>This is a private page for internal employees or members only.</p>
        {/* Add private content here */}
      </Card>
    </div>
  );
}
