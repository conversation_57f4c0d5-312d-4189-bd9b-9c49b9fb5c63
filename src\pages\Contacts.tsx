import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Phone, Mail, MapPin, Clock, Users, Shield, Wrench, Heart } from "lucide-react";

const Contacts = () => {
  const breadcrumbs = [
    { label: "Contacts" }
  ];

  const emergencyContacts = [
    { department: "Security", phone: "123", icon: Shield, description: "24/7 Emergency Security" },
    { department: "Medical", phone: "911", icon: Heart, description: "Medical Emergency" },
    { department: "Maintenance", phone: "456", icon: Wrench, description: "Urgent Repairs" },
  ];

  const departmentContacts = [
    { department: "Human Resources", phone: "+20 12 345 6789", email: "<EMAIL>", icon: Users },
    { department: "Administration", phone: "+20 12 345 6790", email: "<EMAIL>", icon: Users },
    { department: "Finance", phone: "+20 12 345 6791", email: "<EMAIL>", icon: Users },
    { department: "Operations", phone: "+20 12 345 6792", email: "<EMAIL>", icon: Users },
    { department: "Guest Services", phone: "+20 12 345 6793", email: "<EMAIL>", icon: Users },
    { department: "Marketing", phone: "+20 12 345 6794", email: "<EMAIL>", icon: Users },
  ];

  return (
    <PageLayout 
      title="Important Contacts" 
      description="Quick access to all essential contact information for employees and guests."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <ContentSection title="Emergency Contacts" className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {emergencyContacts.map((contact) => (
              <div key={contact.department} className="p-6 rounded-lg bg-red-50 border border-red-200">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center">
                    <contact.icon className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-red-800">{contact.department}</h4>
                    <p className="text-sm text-red-600">{contact.description}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-lg font-bold text-red-800">
                  <Phone className="h-5 w-5" />
                  <span>{contact.phone}</span>
                </div>
              </div>
            ))}
          </div>
        </ContentSection>

        <ContentSection title="Operating Hours">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-corporate-navy" />
              <div>
                <h4 className="font-semibold text-corporate-navy">HR Department</h4>
                <p className="text-sm text-corporate-gray">Sunday - Thursday: 8:00 AM - 5:00 PM</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-corporate-navy" />
              <div>
                <h4 className="font-semibold text-corporate-navy">Reception</h4>
                <p className="text-sm text-corporate-gray">24/7 Available</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-corporate-navy" />
              <div>
                <h4 className="font-semibold text-corporate-navy">Administration</h4>
                <p className="text-sm text-corporate-gray">Sunday - Thursday: 9:00 AM - 6:00 PM</p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Department Contacts">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {departmentContacts.map((contact) => (
            <div key={contact.department} className="p-6 rounded-lg border border-border hover:shadow-card transition-shadow">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                  <contact.icon className="h-6 w-6 text-corporate-navy" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-corporate-navy mb-3">{contact.department}</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-corporate-gray">
                      <Phone className="h-4 w-4" />
                      <a href={`tel:${contact.phone}`} className="hover:text-corporate-navy transition-colors">
                        {contact.phone}
                      </a>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-corporate-gray">
                      <Mail className="h-4 w-4" />
                      <a href={`mailto:${contact.email}`} className="hover:text-corporate-navy transition-colors">
                        {contact.email}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </ContentSection>

      <ContentSection title="Main Office Location">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <MapPin className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Corporate Office</h4>
                <p className="text-corporate-gray">
                  Somabay Peninsula<br />
                  Red Sea Governorate<br />
                  Egypt
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Phone className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Main Reception</h4>
                <p className="text-corporate-gray">+20 65 356 0170</p>
              </div>
            </div>
          </div>

          <div className="bg-corporate-light rounded-lg p-6">
            <h4 className="font-semibold text-corporate-navy mb-4">Quick Reference</h4>
            <div className="space-y-2 text-sm">
              <p className="text-corporate-gray">• Emergency: Dial 123 from any internal phone</p>
              <p className="text-corporate-gray">• External Emergency: +20 65 356 0123</p>
              <p className="text-corporate-gray">• HR Hotline: Available 24/7 for urgent matters</p>
              <p className="text-corporate-gray">• IT Support: Ext. 789 during business hours</p>
            </div>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Contacts;