import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { <PERSON><PERSON><PERSON>, Users, Trophy, Activity } from "lucide-react";

const Clubhouse = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Sports", href: "/destination/sports" },
    { label: "Club House" }
  ];

  return (
    <PageLayout 
      title="Sports Club House" 
      description="Multi-sport complex featuring paddle, football, basketball, squash, and comprehensive fitness facilities."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <ContentSection title="Paddle Courts">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Trophy className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Paddle Tennis</h4>
            <p className="text-corporate-gray text-sm">Professional paddle courts with booking system and equipment rental.</p>
          </div>
        </ContentSection>

        <ContentSection title="Football Field">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Football</h4>
            <p className="text-corporate-gray text-sm">Full-size football field for matches, training, and recreational play.</p>
          </div>
        </ContentSection>

        <ContentSection title="Basketball Court">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Activity className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Basketball</h4>
            <p className="text-corporate-gray text-sm">Indoor basketball court with professional flooring and equipment.</p>
          </div>
        </ContentSection>

        <ContentSection title="Squash Courts">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Dumbbell className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Squash</h4>
            <p className="text-corporate-gray text-sm">Multiple squash courts with glass back walls for viewing and coaching.</p>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Facility Details">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Users className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Multi-Purpose Halls</h4>
                <p className="text-corporate-gray">
                  Flexible indoor spaces that can be configured for various sports, events, and group activities.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Activity className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Professional Equipment</h4>
                <p className="text-corporate-gray">
                  All facilities are equipped with professional-grade equipment and maintained to the highest standards.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Operating Hours</h4>
              <p className="text-corporate-gray text-sm">Daily: 6:00 AM - 11:00 PM</p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Booking System</h4>
              <p className="text-corporate-gray text-sm">Online reservation system available 24/7</p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Equipment Rental</h4>
              <p className="text-corporate-gray text-sm">Full range of sports equipment available for rent</p>
            </div>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Additional Services">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <Trophy className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Tournaments</h4>
            <p className="text-corporate-gray text-sm">Regular tournaments and competitions in all sports.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Group Classes</h4>
            <p className="text-corporate-gray text-sm">Fitness classes, sports clinics, and group training sessions.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Dumbbell className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Personal Training</h4>
            <p className="text-corporate-gray text-sm">One-on-one coaching and personalized fitness programs.</p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Clubhouse;