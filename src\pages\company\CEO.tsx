import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { GraduationCap, Briefcase, Award, TrendingUp } from "lucide-react";

const CEO = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "CEO" }
  ];

  return (
    <PageLayout 
      title="Chief Executive Officer" 
      description="Leadership and vision driving our organization forward."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <ContentSection title="Ibrahim - CEO">
            <div className="text-center">
              <div className="w-32 h-32 rounded-full bg-gradient-primary mx-auto mb-6 flex items-center justify-center">
                <span className="text-4xl font-bold text-white">I</span>
              </div>
              <h3 className="text-xl font-semibold text-corporate-navy mb-2">Ibrahim</h3>
              <p className="text-corporate-gray">Chief Executive Officer</p>
              <div className="mt-4 text-sm text-corporate-gray">
                <p className="flex items-center justify-center gap-2">
                  <Briefcase className="h-4 w-4" />
                  CEO since 2014
                </p>
              </div>
            </div>
          </ContentSection>
        </div>

        <div className="lg:col-span-2 space-y-6">
          <ContentSection title="Leadership Journey">
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                  <Briefcase className="h-6 w-6 text-corporate-navy" />
                </div>
                <div>
                  <h4 className="font-semibold text-corporate-navy mb-2">Current Role (2014 - Present)</h4>
                  <p className="text-corporate-gray">
                    Ibrahim joined Abu Soma Development Company (ASDC) in 2012 as Director of Development and assumed his current role as CEO at the end of 2014. His responsibilities include managing ASDC and the group of companies which ASDC is a majority shareholder in that are operating in Somabay on the Red Sea in Egypt.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-corporate-navy" />
                </div>
                <div>
                  <h4 className="font-semibold text-corporate-navy mb-2">Key Achievements</h4>
                  <p className="text-corporate-gray">
                    During his tenure, Ibrahim developed the company's real estate business, restructured the existing hotels physically, financially & operationally, and established Somabay as a recognized brand and destination.
                  </p>
                </div>
              </div>
            </div>
          </ContentSection>

          <ContentSection title="Professional Background">
            <div className="space-y-4">
              <div className="border-l-4 border-corporate-blue pl-4">
                <h4 className="font-semibold text-corporate-navy">Regional Business Development Director</h4>
                <p className="text-sm text-corporate-gray">CH2MHill, MENAI (2013)</p>
              </div>
              
              <div className="border-l-4 border-corporate-blue pl-4">
                <h4 className="font-semibold text-corporate-navy">Director of Development</h4>
                <p className="text-sm text-corporate-gray">MNHD (2009 to 2012)</p>
              </div>
              
              <div className="border-l-4 border-corporate-blue pl-4">
                <h4 className="font-semibold text-corporate-navy">Director of Development, North Africa</h4>
                <p className="text-sm text-corporate-gray">Limitless [a Dubai World Company] (2008 to 2009)</p>
              </div>
              
              <div className="border-l-4 border-corporate-blue pl-4">
                <h4 className="font-semibold text-corporate-navy">Business / Commercial and Project Controls Manager</h4>
                <p className="text-sm text-corporate-gray">Bechtel, across 5 countries (1994 to 2008)</p>
              </div>
            </div>
          </ContentSection>
        </div>
      </div>

      <ContentSection title="Education & Qualifications">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <GraduationCap className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Bachelor's Degree</h4>
              <p className="text-corporate-gray">
                B.Sc. in Construction Engineering with High Honors<br />
                American University in Cairo (1994)
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Award className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Master's Degree</h4>
              <p className="text-corporate-gray">
                M.Sc. in Built Environment<br />
                University College London (1999)
              </p>
            </div>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default CEO;