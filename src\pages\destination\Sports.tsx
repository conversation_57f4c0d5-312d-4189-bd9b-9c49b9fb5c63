import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Trophy, Target, Dumbbell, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Sports = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Sports" }
  ];

  return (
    <PageLayout 
      title="Sports & Recreation" 
      description="World-class sports facilities and recreational activities for all ages and skill levels."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <ContentSection title="Golf Course">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Target className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Championship Golf</h4>
            <p className="text-corporate-gray">18-hole championship golf course designed by <PERSON> with stunning Red Sea views.</p>
          </div>
        </ContentSection>

        <ContentSection title="Tennis Academy">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Trophy className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Tennis Excellence</h4>
            <p className="text-corporate-gray">Professional tennis courts with coaching programs for all levels, from beginners to advanced players.</p>
          </div>
        </ContentSection>

        <ContentSection title="Club House">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-4">
              <Dumbbell className="h-8 w-8 text-white" />
            </div>
            <h4 className="font-semibold text-corporate-navy mb-2">Multi-Sport Complex</h4>
            <p className="text-corporate-gray">Comprehensive sports facility including paddle, football, basketball, squash, and fitness center.</p>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Water Sports">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Diving Center</h4>
            <p className="text-corporate-gray text-sm">PADI certified diving center with equipment rental and courses.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Kite Surfing</h4>
            <p className="text-corporate-gray text-sm">Perfect wind conditions for kite surfing with professional instruction.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Snorkeling</h4>
            <p className="text-corporate-gray text-sm">Guided snorkeling tours to explore pristine coral reefs.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Water Sports</h4>
            <p className="text-corporate-gray text-sm">Jet skiing, banana boats, and other exciting water activities.</p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Fitness & Wellness">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Dumbbell className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Modern Gym</h4>
              <p className="text-corporate-gray">
                Fully equipped fitness center with the latest exercise equipment, personal trainers, and group fitness classes.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Users className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Group Activities</h4>
              <p className="text-corporate-gray">
                Regular group activities including yoga, aerobics, aqua fitness, and team sports tournaments.
              </p>
            </div>
          </div>
        </div>
      </ContentSection>
      <div className="text-center mt-8">
        <Button asChild>
          <Link to="/destination/read-all">Read All</Link>
        </Button>
      </div>
    </PageLayout>
  );
};

export default Sports;
