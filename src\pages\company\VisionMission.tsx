import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Target, Compass } from "lucide-react";

const VisionMission = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "Vision & Mission" }
  ];

  return (
    <PageLayout 
      title="Vision & Mission" 
      description="Our guiding principles that drive everything we do."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Our Vision">
          <div className="text-center py-8">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-6">
              <Target className="h-8 w-8 text-white" />
            </div>
            <blockquote className="text-xl font-medium text-corporate-navy leading-relaxed">
              "Redefining luxury living and hospitality into a seamless, sustainable lifestyle."
            </blockquote>
          </div>
        </ContentSection>

        <ContentSection title="Our Mission">
          <div className="text-center py-8">
            <div className="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center mx-auto mb-6">
              <Compass className="h-8 w-8 text-white" />
            </div>
            <blockquote className="text-xl font-medium text-corporate-navy leading-relaxed">
              "Deliver unparalleled hospitality and living experiences, while fostering a sustainable, thriving community for residents and travelers alike."
            </blockquote>
          </div>
        </ContentSection>
      </div>

      <ContentSection 
        title="What This Means for Us"
        description="How our vision and mission translate into daily operations"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Luxury Redefined</h4>
            <p className="text-corporate-gray">
              We go beyond traditional luxury to create meaningful, sustainable experiences that enhance life.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Seamless Integration</h4>
            <p className="text-corporate-gray">
              Our services blend hospitality and living into one cohesive, exceptional lifestyle experience.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Community Focus</h4>
            <p className="text-corporate-gray">
              We build thriving communities that benefit both residents and visitors through authentic connections.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Sustainability First</h4>
            <p className="text-corporate-gray">
              Environmental responsibility is woven into every aspect of our operations and future planning.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Unparalleled Service</h4>
            <p className="text-corporate-gray">
              Every interaction is an opportunity to exceed expectations and create lasting memories.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-3">Long-term Thinking</h4>
            <p className="text-corporate-gray">
              Our decisions today shape the sustainable, thriving communities of tomorrow.
            </p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default VisionMission;