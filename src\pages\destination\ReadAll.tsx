import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { MapPin, Globe, Building, BedDouble, Trophy, Sun } from "lucide-react";

const ReadAll = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Read All" }
  ];

  return (
    <PageLayout 
      title="About The Destination" 
      description="A comprehensive overview of Somabay's premium location, residential projects, hotels, sports facilities, and sustainability initiatives."
      breadcrumbs={breadcrumbs}
    >
      <ContentSection title="Destination Premium Location">
        <div className="space-y-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <MapPin className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Ideal Location</h4>
              <p className="text-corporate-gray">
                Situated on the eastern shores of Egypt along the captivating Red Sea coast, Somabay offers an ideal location just a convenient 20-minute drive from Hurghada International Airport. Accessible with a mere 4-hour flight from Central Europe, this expansive, self-contained community spans an impressive ten million square meters, enveloped by the sea on three sides.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Globe className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Natural Beauty</h4>
              <p className="text-corporate-gray">
                Boasting of some of the region’s most stunning sandy beaches and breathtaking panoramic vistas of desert mountains, Somabay promises a picturesque resort experience. Nestled within 10 million square meters of verdant landscapes, life at Somabay transports you into a world where fantasy-scapes seamlessly blend with reality, and sun-drenched activities offer endless possibilities for elevation and enjoyment.
              </p>
            </div>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Residential Projects">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Golf Town Houses</h4>
            <p className="text-corporate-gray">85 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Somabreeze</h4>
            <p className="text-corporate-gray">350 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Reeftown</h4>
            <p className="text-corporate-gray">190 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Arc of Soma</h4>
            <p className="text-corporate-gray">165 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Soma Coves</h4>
            <p className="text-corporate-gray">100 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Wadi Jebal & WJE</h4>
            <p className="text-corporate-gray">138 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Marina Residences</h4>
            <p className="text-corporate-gray">128 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Bay West</h4>
            <p className="text-corporate-gray">148 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Mesca</h4>
            <p className="text-corporate-gray">144 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Bay Central Villas (Nautilas)</h4>
            <p className="text-corporate-gray">10 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Mesca Chalets</h4>
            <p className="text-corporate-gray">36 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Mesca Cabanas</h4>
            <p className="text-corporate-gray">186 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Bay Central</h4>
            <p className="text-corporate-gray">136 Units</p>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Blanca</h4>
            <p className="text-corporate-gray">36 Units</p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Existing Hotels">
        <div className="flex items-start gap-4">
          <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
            <BedDouble className="h-6 w-6 text-corporate-navy" />
          </div>
          <div>
            <h4 className="font-semibold text-corporate-navy mb-2">326 Rooms</h4>
            <p className="text-corporate-gray">
              Our existing hotels offer a total of 326 rooms, providing world-class hospitality and stunning Red Sea views.
            </p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Sports">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">18-Hole Gary Player Course</h4>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">18-Hole New Course</h4>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">9-Hole Course</h4>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Golf Academy</h4>
          </div>
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">Driving Range</h4>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Sustainability – Solar & Natural Gas">
        <div className="space-y-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Sun className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Solar Power Plant</h4>
              <p className="text-corporate-gray">
                Produces 4 mega daily.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Building className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Utility Centre</h4>
              <p className="text-corporate-gray">
                Established in 1997, was one of the biggest utility centers in the Red Sea. Produces 1,220 Cubic meter/Daily.
              </p>
            </div>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default ReadAll;
