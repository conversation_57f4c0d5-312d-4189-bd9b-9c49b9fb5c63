import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";

const Attendance = () => {
  const breadcrumbs = [
    { label: "Policies", href: "/policies" },
    { label: "Attendance" }
  ];

  return (
    <PageLayout 
      title="Attendance Policies" 
      description="Working hours, sign-in procedures, tardiness policies, and holiday work guidelines."
      breadcrumbs={breadcrumbs}
    >
      <ContentSection title="Attendance Guidelines">
        <p className="text-corporate-gray">Comprehensive attendance policies covering working hours, sign-in/out procedures, tardiness, permissions, and working on official holidays and weekends.</p>
      </ContentSection>
    </PageLayout>
  );
};

export default Attendance;