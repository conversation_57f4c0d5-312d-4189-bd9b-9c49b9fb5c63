import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";

const TalentManagement = () => {
  const breadcrumbs = [
    { label: "Policies", href: "/policies" },
    { label: "Talent Management" }
  ];

  return (
    <PageLayout 
      title="Talent Management Policies" 
      description="Comprehensive policies covering hiring, learning & development, performance, succession planning, and promotions."
      breadcrumbs={breadcrumbs}
    >
      <ContentSection title="Talent Management Overview">
        <p className="text-corporate-gray">Our talent management framework includes hiring practices, learning and development programs, performance management, succession planning, and promotion policies.</p>
      </ContentSection>
    </PageLayout>
  );
};

export default TalentManagement;