import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Users, UserCog, Building } from "lucide-react";

const ExecutiveChart = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "Executive Chart" }
  ];

  return (
    <PageLayout 
      title="Executive Chart" 
      description="Our organizational structure and leadership hierarchy."
      breadcrumbs={breadcrumbs}
    >
      <ContentSection title="Organizational Structure">
        <div className="space-y-8">
          {/* CEO Level */}
          <div className="text-center">
            <div className="inline-block p-6 bg-gradient-primary rounded-lg text-white">
              <UserCog className="h-8 w-8 mx-auto mb-2" />
              <h3 className="text-xl font-semibold">Ibrahim</h3>
              <p className="text-sm opacity-90">Chief Executive Officer</p>
            </div>
          </div>

          {/* Executive Level */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div className="text-center p-6 bg-card border border-border rounded-lg">
              <Users className="h-6 w-6 text-corporate-navy mx-auto mb-3" />
              <h4 className="font-semibold text-corporate-navy mb-1">Operations Director</h4>
              <p className="text-sm text-corporate-gray">Overseeing daily operations and service delivery</p>
            </div>
            
            <div className="text-center p-6 bg-card border border-border rounded-lg">
              <Building className="h-6 w-6 text-corporate-navy mx-auto mb-3" />
              <h4 className="font-semibold text-corporate-navy mb-1">Development Director</h4>
              <p className="text-sm text-corporate-gray">Leading real estate and facility development</p>
            </div>
            
            <div className="text-center p-6 bg-card border border-border rounded-lg">
              <UserCog className="h-6 w-6 text-corporate-navy mx-auto mb-3" />
              <h4 className="font-semibold text-corporate-navy mb-1">General Manager</h4>
              <p className="text-sm text-corporate-gray">Managing hospitality and guest services</p>
            </div>
          </div>

          {/* Department Level */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">HR Department</h5>
              <p className="text-xs text-corporate-gray">Human Resources</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Finance</h5>
              <p className="text-xs text-corporate-gray">Financial Management</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Engineering</h5>
              <p className="text-xs text-corporate-gray">Technical Services</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Marketing</h5>
              <p className="text-xs text-corporate-gray">Brand & Communications</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Security</h5>
              <p className="text-xs text-corporate-gray">Safety & Security</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Housekeeping</h5>
              <p className="text-xs text-corporate-gray">Facility Maintenance</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">F&B</h5>
              <p className="text-xs text-corporate-gray">Food & Beverage</p>
            </div>
            
            <div className="text-center p-4 bg-corporate-light rounded-lg">
              <h5 className="font-medium text-corporate-navy mb-1">Guest Relations</h5>
              <p className="text-xs text-corporate-gray">Customer Service</p>
            </div>
          </div>
        </div>
      </ContentSection>

      <ContentSection 
        title="Reporting Structure"
        description="How our organization communicates and collaborates"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Direct Reporting</h4>
            <p className="text-corporate-gray">
              Department heads report directly to the executive team for strategic alignment.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Cross-Functional Teams</h4>
            <p className="text-corporate-gray">
              Project teams combine expertise from multiple departments for optimal results.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Open Communication</h4>
            <p className="text-corporate-gray">
              All levels maintain open channels for feedback and continuous improvement.
            </p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default ExecutiveChart;