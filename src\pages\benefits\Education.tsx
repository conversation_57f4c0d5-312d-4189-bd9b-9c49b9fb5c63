import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { School, GraduationCap, BookOpen, Award } from "lucide-react";

const Education = () => {
  const breadcrumbs = [
    { label: "Our Benefits", href: "/benefits" },
    { label: "Education" }
  ];

  return (
    <PageLayout 
      title="Educational Benefits" 
      description="Comprehensive educational support for employees and their families, from primary school through university."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="School Benefits" id="school">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <School className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Primary & Secondary Education</h4>
                <p className="text-corporate-gray">
                  Full or partial tuition coverage for employees' children attending local and international schools, ensuring quality education opportunities.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Educational Materials</h4>
                <p className="text-corporate-gray">
                  Annual allowance for books, supplies, uniforms, and other educational materials required for school attendance.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="University Benefits" id="university">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Higher Education Support</h4>
                <p className="text-corporate-gray">
                  Tuition assistance for employees pursuing professional development and for dependents attending university programs.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Award className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Scholarship Programs</h4>
                <p className="text-corporate-gray">
                  Merit-based scholarships for outstanding academic performance and financial need assistance for qualifying families.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Educational Support Details">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Eligibility Requirements</h4>
            <ul className="space-y-2 text-corporate-gray text-sm">
              <li>• Full-time employment (minimum 1 year)</li>
              <li>• Dependent children under 25</li>
              <li>• Enrollment in accredited institutions</li>
              <li>• Maintaining satisfactory grades</li>
            </ul>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Coverage Levels</h4>
            <ul className="space-y-2 text-corporate-gray text-sm">
              <li>• Primary School: 100% coverage</li>
              <li>• Secondary School: 100% coverage</li>
              <li>• University: Up to 75% coverage</li>
              <li>• Professional Development: 100%</li>
            </ul>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">Additional Benefits</h4>
            <ul className="space-y-2 text-corporate-gray text-sm">
              <li>• Educational counseling services</li>
              <li>• Academic performance tracking</li>
              <li>• Study abroad programs</li>
              <li>• Language learning support</li>
            </ul>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Application Process">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 rounded-lg bg-gradient-primary text-white">
            <h4 className="text-xl font-semibold mb-4">How to Apply</h4>
            <ol className="space-y-2 text-white/90">
              <li>1. Complete education benefit application form</li>
              <li>2. Submit academic transcripts and enrollment proof</li>
              <li>3. Provide tuition fee documentation</li>
              <li>4. HR review and approval process</li>
              <li>5. Direct payment or reimbursement</li>
            </ol>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="text-xl font-semibold text-corporate-navy mb-4">Important Deadlines</h4>
            <ul className="space-y-2 text-corporate-gray">
              <li>• Fall semester: Applications due July 15</li>
              <li>• Spring semester: Applications due December 15</li>
              <li>• Summer programs: Applications due April 15</li>
              <li>• Emergency assistance: Anytime throughout year</li>
            </ul>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Education;