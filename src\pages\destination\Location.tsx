import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { MapPin, Globe, Plane, Car } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Location = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Prime Location" }
  ];

  return (
    <PageLayout 
      title="Prime Location" 
      description="Discover our exceptional location on the Red Sea, offering unparalleled access to natural beauty and modern amenities."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Somabay, Red Sea">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <MapPin className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Premium Location</h4>
                <p className="text-corporate-gray">
                  Situated on the eastern shores of Egypt along the captivating Red Sea coast, Somabay offers an ideal location just a convenient 20-minute drive from Hurghada International Airport. This expansive, self-contained community spans an impressive ten million square meters, enveloped by the sea on three sides.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Globe className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Accessibility</h4>
                <p className="text-corporate-gray">
                  Accessible with a mere 4-hour flight from Central Europe, making it an easily reachable destination for international visitors and residents.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Natural Beauty">
          <div className="space-y-4">
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Stunning Sandy Beaches</h4>
              <p className="text-corporate-gray">
                Boasting some of the region's most stunning sandy beaches with crystal-clear waters perfect for relaxation and water activities.
              </p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Panoramic Desert Views</h4>
              <p className="text-corporate-gray">
                Breathtaking panoramic vistas of desert mountains creating a picturesque resort experience.
              </p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Verdant Landscapes</h4>
              <p className="text-corporate-gray">
                Nestled within 10 million square meters of verdant landscapes where fantasy-scapes seamlessly blend with reality.
              </p>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Accessibility">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Plane className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Airport Access</h4>
              <p className="text-corporate-gray">
                Just 20 minutes from Hurghada International Airport, with direct flights from major European cities and connecting flights worldwide.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
              <Car className="h-6 w-6 text-corporate-navy" />
            </div>
            <div>
              <h4 className="font-semibold text-corporate-navy mb-2">Road Connections</h4>
              <p className="text-corporate-gray">
                Well-connected by modern highways to Cairo (4 hours), Luxor (3 hours), and other major Egyptian destinations.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 p-6 rounded-lg bg-gradient-primary text-white">
          <h4 className="text-xl font-semibold mb-4">Why Our Location Matters</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h5 className="font-medium mb-2">Year-Round Climate</h5>
              <p className="text-white/90 text-sm">Perfect weather conditions for outdoor activities throughout the year.</p>
            </div>
            <div>
              <h5 className="font-medium mb-2">Protected Environment</h5>
              <p className="text-white/90 text-sm">Carefully preserved ecosystem ensuring sustainable development.</p>
            </div>
            <div>
              <h5 className="font-medium mb-2">Exclusive Access</h5>
              <p className="text-white/90 text-sm">Private peninsula providing exclusivity and privacy for residents and guests.</p>
            </div>
          </div>
        </div>
      </ContentSection>
      <div className="text-center mt-8">
        <Button asChild>
          <Link to="/destination/read-all">Read All</Link>
        </Button>
      </div>
    </PageLayout>
  );
};

export default Location;
