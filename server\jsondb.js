import fs from 'fs';
import path from 'path';

const DB_FILE = path.resolve(process.cwd(), '../policyhub.json');

function readDB() {
  if (!fs.existsSync(DB_FILE)) {
    fs.writeFileSync(DB_FILE, JSON.stringify({ users: [], pages: [] }, null, 2));
  }
  return JSON.parse(fs.readFileSync(DB_FILE, 'utf-8'));
}

function writeDB(data) {
  fs.writeFileSync(DB_FILE, JSON.stringify(data, null, 2));
}

export function getUsers() {
  return readDB().users;
}

export function addUser(user) {
  const db = readDB();
  db.users.push(user);
  writeDB(db);
}

export function getPages() {
  return readDB().pages;
}

export function addPage(page) {
  const db = readDB();
  db.pages.push(page);
  writeDB(db);
}

export function updatePage(id, newPage) {
  const db = readDB();
  const idx = db.pages.findIndex(p => p.id === id);
  if (idx !== -1) {
    db.pages[idx] = { ...db.pages[idx], ...newPage };
    writeDB(db);
    return true;
  }
  return false;
}

export function deletePage(id) {
  const db = readDB();
  db.pages = db.pages.filter(p => p.id !== id);
  writeDB(db);
}
