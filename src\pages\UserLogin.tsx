import React, { useState } from "react";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Card } from "../components/ui/card";

const USER_CREDENTIALS = [
  { username: "employee1", password: "password1" },
  { username: "member1", password: "password2" },
];

export default function UserLogin({ onLogin }: { onLogin: (user: string) => void }) {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    const user = USER_CREDENTIALS.find(
      (u) => u.username === username && u.password === password
    );
    if (user) {
      onLogin(user.username);
    } else {
      setError("Invalid username or password");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Card className="p-8 w-full max-w-sm">
        <h2 className="text-xl font-bold mb-4">Member/Employee Login</h2>
        <form onSubmit={handleLogin} className="flex flex-col gap-4">
          <Input
            type="text"
            placeholder="Username"
            value={username}
            onChange={e => setUsername(e.target.value)}
          />
          <Input
            type="password"
            placeholder="Password"
            value={password}
            onChange={e => setPassword(e.target.value)}
          />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <Button type="submit">Login</Button>
        </form>
      </Card>
    </div>
  );
}
