import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Home, Key, Shield, Wifi } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Residentials = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Residentials" }
  ];

  return (
    <PageLayout 
      title="Residential Options" 
      description="Comfortable living spaces designed for long-term residents and extended stays."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <ContentSection title="Golf Town Houses">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">85 Units</h4>
            <p className="text-corporate-gray">Luxury townhouses with stunning golf course views.</p>
          </div>
        </ContentSection>
        <ContentSection title="Somabreeze">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">350 Units</h4>
            <p className="text-corporate-gray">Modern apartments with refreshing sea breezes.</p>
          </div>
        </ContentSection>
        <ContentSection title="Reeftown">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">190 Units</h4>
            <p className="text-corporate-gray">Vibrant community with easy access to coral reefs.</p>
          </div>
        </ContentSection>
        <ContentSection title="Arc of Soma">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">165 Units</h4>
            <p className="text-corporate-gray">Elegant residences with panoramic sea views.</p>
          </div>
        </ContentSection>
        <ContentSection title="Soma Coves">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">100 Units</h4>
            <p className="text-corporate-gray">Exclusive villas nestled in secluded coves.</p>
          </div>
        </ContentSection>
        <ContentSection title="Wadi Jebal & WJE">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">138 Units</h4>
            <p className="text-corporate-gray">Luxurious lodges with breathtaking mountain views.</p>
          </div>
        </ContentSection>
        <ContentSection title="Marina Residences">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">128 Units</h4>
            <p className="text-corporate-gray">Stylish apartments overlooking the marina.</p>
          </div>
        </ContentSection>
        <ContentSection title="Bay West">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">148 Units</h4>
            <p className="text-corporate-gray">Contemporary homes with stunning bay views.</p>
          </div>
        </ContentSection>
        <ContentSection title="Mesca">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">144 Units</h4>
            <p className="text-corporate-gray">Spacious residences with modern amenities.</p>
          </div>
        </ContentSection>
        <ContentSection title="Bay Central Villas (Nautilas)">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">10 Units</h4>
            <p className="text-corporate-gray">Exclusive villas with premium finishes.</p>
          </div>
        </ContentSection>
        <ContentSection title="Mesca Chalets">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">36 Units</h4>
            <p className="text-corporate-gray">Cozy chalets perfect for a getaway.</p>
          </div>
        </ContentSection>
        <ContentSection title="Mesca Cabanas">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">186 Units</h4>
            <p className="text-corporate-gray">Charming cabanas with direct beach access.</p>
          </div>
        </ContentSection>
        <ContentSection title="Bay Central">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">136 Units</h4>
            <p className="text-corporate-gray">Central apartments with convenient access to amenities.</p>
          </div>
        </ContentSection>
        <ContentSection title="Blanca">
          <div className="p-4 rounded-lg bg-corporate-light">
            <h4 className="font-semibold text-corporate-navy mb-2">36 Units</h4>
            <p className="text-corporate-gray">Elegant homes with minimalist design.</p>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Amenities & Services">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <Shield className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">24/7 Security</h4>
            <p className="text-corporate-gray text-sm">Gated community with round-the-clock security service.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Wifi className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">High-Speed Internet</h4>
            <p className="text-corporate-gray text-sm">Fiber-optic internet connectivity throughout all units.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Home className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Maintenance</h4>
            <p className="text-corporate-gray text-sm">Comprehensive maintenance and housekeeping services.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Key className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Concierge</h4>
            <p className="text-corporate-gray text-sm">Personal concierge service for all your needs.</p>
          </div>
        </div>
      </ContentSection>
      <div className="text-center mt-8">
        <Button asChild>
          <Link to="/destination/read-all">Read All</Link>
        </Button>
      </div>
    </PageLayout>
  );
};

export default Residentials;
