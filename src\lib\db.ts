import Database from 'better-sqlite3';
import path from 'path';

// Database file will be stored in the project root as 'policyhub.db'
const dbPath = path.resolve(process.cwd(), 'policyhub.db');
const db = new Database(dbPath);

// Create users table if it doesn't exist
const createUsersTable = `
CREATE TABLE IF NOT EXISTS users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT UNIQUE NOT NULL,
  password TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member'
);
`;
db.exec(createUsersTable);

// Create pages table if it doesn't exist
const createPagesTable = `
CREATE TABLE IF NOT EXISTS pages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  content TEXT NOT NULL,
  is_private INTEGER NOT NULL DEFAULT 0
);
`;
db.exec(createPagesTable);

export default db;
