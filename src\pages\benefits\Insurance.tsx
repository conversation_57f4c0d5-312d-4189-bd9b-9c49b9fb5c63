import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Shield, HeartHandshake, Users, FileCheck } from "lucide-react";

const Insurance = () => {
  const breadcrumbs = [
    { label: "Our Benefits", href: "/benefits" },
    { label: "Insurance" }
  ];

  return (
    <PageLayout 
      title="Insurance Coverage" 
      description="Comprehensive insurance benefits protecting you and your family's health, life, and social security."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        <ContentSection title="Medical Insurance" id="medical">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                  <HeartHandshake className="h-6 w-6 text-corporate-navy" />
                </div>
                <div>
                  <h4 className="font-semibold text-corporate-navy mb-2">Comprehensive Coverage</h4>
                  <p className="text-corporate-gray">
                    Full medical coverage including hospitalization, outpatient care, emergency services, and specialist consultations.
                  </p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg bg-corporate-light">
                <h5 className="font-medium text-corporate-navy mb-2">Coverage Details</h5>
                <ul className="space-y-1 text-corporate-gray text-sm">
                  <li>• 100% hospitalization coverage</li>
                  <li>• Outpatient visits and consultations</li>
                  <li>• Prescription medications</li>
                  <li>• Dental and vision care</li>
                  <li>• Emergency medical services</li>
                </ul>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 rounded-lg border border-border">
                <h5 className="font-medium text-corporate-navy mb-2">Family Coverage</h5>
                <p className="text-corporate-gray text-sm mb-3">
                  Medical insurance extends to spouse and dependent children up to age 25.
                </p>
                <ul className="space-y-1 text-corporate-gray text-sm">
                  <li>• Employee: 100% premium covered</li>
                  <li>• Spouse: 100% premium covered</li>
                  <li>• Children: 100% premium covered</li>
                  <li>• Pre-existing conditions covered</li>
                </ul>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Life Insurance" id="life">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Shield className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Life Insurance Coverage</h4>
                <p className="text-corporate-gray">
                  Term life insurance providing financial security for your beneficiaries in case of death or permanent disability.
                </p>
              </div>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h5 className="font-medium text-corporate-navy mb-2">Coverage Amount</h5>
              <ul className="space-y-1 text-corporate-gray text-sm">
                <li>• Base coverage: 2x annual salary</li>
                <li>• Additional voluntary coverage available</li>
                <li>• Accidental death & dismemberment</li>
                <li>• No medical exam required</li>
              </ul>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Social Insurance" id="social">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Users className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Social Security Benefits</h4>
                <p className="text-corporate-gray">
                  Comprehensive social insurance coverage providing retirement, disability, and unemployment benefits.
                </p>
              </div>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h5 className="font-medium text-corporate-navy mb-2">Benefits Include</h5>
              <ul className="space-y-1 text-corporate-gray text-sm">
                <li>• Retirement pension</li>
                <li>• Disability compensation</li>
                <li>• Unemployment benefits</li>
                <li>• Work injury coverage</li>
                <li>• Survivor benefits</li>
              </ul>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Claims Process">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <FileCheck className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Submit Claim</h4>
            <p className="text-corporate-gray text-sm">Complete claim forms and submit required documentation to HR.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Review Process</h4>
            <p className="text-corporate-gray text-sm">Claims are reviewed and processed within 5-10 business days.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Shield className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Payment</h4>
            <p className="text-corporate-gray text-sm">Approved claims are paid directly to providers or reimbursed to you.</p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Insurance;