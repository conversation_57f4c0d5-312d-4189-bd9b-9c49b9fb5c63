import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Trophy, Users, Award, Clock } from "lucide-react";

const Tennis = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Sports", href: "/destination/sports" },
    { label: "Tennis Academy" }
  ];

  return (
    <PageLayout 
      title="Tennis Academy" 
      description="Professional tennis facilities and coaching programs for players of all levels."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Court Facilities">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Trophy className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Professional Courts</h4>
                <p className="text-corporate-gray">
                  Six regulation tennis courts with professional-grade surfaces, including both hard courts and clay courts for varied playing experiences.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Clock className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Lighting System</h4>
                <p className="text-corporate-gray">
                  All courts feature professional lighting systems, allowing for evening play and extended playing hours.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Court Specifications">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">6</h4>
                <p className="text-sm text-corporate-gray">Tennis Courts</p>
              </div>
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">2</h4>
                <p className="text-sm text-corporate-gray">Court Surfaces</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">LED</h4>
                <p className="text-sm text-corporate-gray">Lighting</p>
              </div>
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">24/7</h4>
                <p className="text-sm text-corporate-gray">Access</p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Tennis Programs">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Group Lessons</h4>
            <p className="text-corporate-gray text-sm">Small group coaching sessions for beginners to advanced players.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Award className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Private Coaching</h4>
            <p className="text-corporate-gray text-sm">One-on-one instruction with certified tennis professionals.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Trophy className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Junior Academy</h4>
            <p className="text-corporate-gray text-sm">Specialized programs for young players aged 6-18.</p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Academy Features">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 rounded-lg bg-gradient-primary text-white">
            <h4 className="text-xl font-semibold mb-4">Professional Coaching</h4>
            <ul className="space-y-2 text-white/90">
              <li>• Certified tennis professionals</li>
              <li>• Video analysis sessions</li>
              <li>• Fitness and conditioning</li>
              <li>• Tournament preparation</li>
              <li>• Match play opportunities</li>
            </ul>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="text-xl font-semibold text-corporate-navy mb-4">Academy Services</h4>
            <ul className="space-y-2 text-corporate-gray">
              <li>• Equipment rental and sales</li>
              <li>• String and racquet services</li>
              <li>• Court booking system</li>
              <li>• Tournament organization</li>
              <li>• Social tennis events</li>
            </ul>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Tennis;