import { HandbookCard } from "@/components/HandbookCard"
import { <PERSON>bar<PERSON>rovider, SidebarTrigger } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/AppSidebar"
import { BookOpen, Users, Calendar, Heart, ShieldCheck, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>lipboardList, FileText } from "lucide-react"

// Import images
import employmentImage from "@/assets/employment-provisions.jpg"
import attendanceImage from "@/assets/attendance-leaves.jpg"
import benefitsImage from "@/assets/benefits.jpg"
import policiesImage from "@/assets/policies.jpg"
import disciplinaryImage from "@/assets/disciplinary.jpg"
import formsImage from "@/assets/forms.jpg"

const handbookSections = [
  {
    title: "Employment General Provisions",
    description: "Comprehensive guidelines covering all aspects of employment policies, from hiring practices to general workplace standards.",
    items: [
      "Categories of Employment",
      "Employment Procedures",
      "Orientation Process",
      "Job Classifications",
      "Workplace Policy Overview",
      "General Terms of Employment"
    ],
    image: employmentImage,
    imageAlt: "Professional business people representing employment provisions",
    href: "/employment"
  },
  {
    title: "Attendance, Holidays, and Leaves",
    description: "Detailed information about work schedules, vacation policies, sick leave, and other time-off benefits.",
    items: [
      "University Operating Hours and Work Week",
      "Attendance Requirements",
      "Annual Leave",
      "Sick Leave",
      "Personal Leave",
      "Parental Leave",
      "Emergency and Special Leave",
      "Holiday Schedule",
      "Official Holidays"
    ],
    image: attendanceImage,
    imageAlt: "Work-life balance concept with dice showing work and life",
    href: "/attendance"
  },
  {
    title: "Benefits",
    description: "Complete overview of employee benefits including health insurance, retirement plans, and additional perks.",
    items: [
      "Health Insurance Plans",
      "Group Life Insurance Plan",
      "Accidental Death and Dismemberment",
      "Eye Care Benefits",
      "Educational Benefits for Staff Members and their Dependents",
      "Social Insurance/Social Security"
    ],
    image: benefitsImage,
    imageAlt: "Corporate benefits concept with medical and insurance symbols",
    href: "/benefits"
  },
  {
    title: "Policies",
    description: "Essential workplace policies covering professional conduct, ethical guidelines, and operational standards.",
    items: [
      "Equal Employment Opportunity",
      "Anti-Harassment and Non-Discrimination",
      "Code of Ethics Policy",
      "Sexual Harassment",
      "Drug and Alcohol-Free Workplace",
      "Information Security",
      "Professional Development",
      "Telecommunications and Internet Networking"
    ],
    image: policiesImage,
    imageAlt: "Corporate policies with honesty is the best policy message",
    href: "/policies"
  },
  {
    title: "Disciplinary Procedures",
    description: "Clear guidelines on disciplinary actions, investigation processes, and resolution procedures.",
    items: [
      "Disciplinary Action and Investigation",
      "Dismissal Procedures"
    ],
    image: disciplinaryImage,
    imageAlt: "Professional meeting room representing disciplinary procedures",
    href: "/disciplinary"
  },
  {
    title: "Forms",
    description: "Access to all necessary HR forms and documentation for various employee processes and requests.",
    items: [
      "HR Letter Request Form",
      "Leave Forms",
      "Medical and Dental Forms",
      "Separation Form",
      "Scholarship Forms - Staff Forms",
      "Training Request Form",
      "Health Insurance Plan Forms",
      "Eye Care Form",
      "Life Insurance Beneficiary Request Form",
      "Confidential Information Non-Disclosure Forms"
    ],
    image: formsImage,
    imageAlt: "Professional forms and paperwork on desk",
    href: "/forms"
  }
]

const Index = () => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <header className="sticky top-0 z-40 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60">
            <div className="flex h-16 items-center gap-4 px-6">
              <SidebarTrigger className="text-corporate-navy hover:bg-corporate-light" />
              <div className="flex items-center gap-2">
                <BookOpen className="h-6 w-6 text-corporate-navy" />
                <div>
                  <h1 className="text-xl font-bold text-corporate-navy">
                    Policies and Procedures Staff Manual
                  </h1>
                  <p className="text-sm text-corporate-gray">
                    Employee Handbook & HR Resources
                  </p>
                </div>
              </div>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              {/* Welcome Section */}
              <div className="mb-12">
                <div className="bg-gradient-primary rounded-2xl p-8 lg:p-12 text-center text-white shadow-elevated">
                  <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                    Welcome to the Employee Handbook
                  </h2>
                  <p className="text-lg lg:text-xl text-white/90 max-w-3xl mx-auto">
                    Your comprehensive guide to company policies, procedures, and benefits. 
                    Everything you need to know about working with us is organized into 
                    easy-to-navigate sections below.
                  </p>
                </div>
              </div>

              {/* Handbook Sections */}
              <div className="space-y-8">
                {handbookSections.map((section, index) => (
                  <HandbookCard
                    key={section.title}
                    title={section.title}
                    description={section.description}
                    items={section.items}
                    image={section.image}
                    imageAlt={section.imageAlt}
                    href={section.href}
                    className={index % 2 === 1 ? "lg:flex-row-reverse" : ""}
                  />
                ))}
              </div>

              {/* Guidelines Section */}
              <div className="mt-16 pt-12 border-t border-border">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-corporate-navy mb-4">
                    Additional Guidelines
                  </h3>
                  <p className="text-corporate-gray max-w-2xl mx-auto">
                    Important supplementary documents and resources to support your work experience.
                  </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    "FY24 Annual Leave Plans",
                    "Teaching Assignments for Full-Time Staff Members and Adjuncts",
                    "Casual Process Guidelines",
                    "Service Agreements Tracking",
                    "Staff ADAA FAQ",
                    "North America Personnel Policy and Procedures Handbook",
                    "Remote Work Arrangements",
                    "Remote Work Procedures"
                  ].map((guideline) => (
                    <div 
                      key={guideline}
                      className="bg-card border border-border rounded-lg p-6 hover:shadow-card transition-shadow"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-corporate-blue mt-2 shrink-0" />
                        <h4 className="font-medium text-corporate-navy">{guideline}</h4>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  )
}

export default Index