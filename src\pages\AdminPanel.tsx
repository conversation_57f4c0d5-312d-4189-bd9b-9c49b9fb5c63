import React from "react";
import { useState } from "react";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Card } from "../components/ui/card";

// Placeholder for authentication (simple password)
const ADMIN_PASSWORD = "admin123";

function AdminLogin({ onLogin }: { onLogin: () => void }) {
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (password === ADMIN_PASSWORD) {
      onLogin();
    } else {
      setError("Incorrect password");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh]">
      <Card className="p-8 w-full max-w-sm">
        <h2 className="text-xl font-bold mb-4">Admin <PERSON>gin</h2>
        <form onSubmit={handleLogin} className="flex flex-col gap-4">
          <Input
            type="password"
            placeholder="Enter admin password"
            value={password}
            onChange={e => setPassword(e.target.value)}
          />
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <Button type="submit">Login</Button>
        </form>
      </Card>
    </div>
  );
}

export default function AdminPanel() {
  const [loggedIn, setLoggedIn] = useState(false);

  if (!loggedIn) {
    return <AdminLogin onLogin={() => setLoggedIn(true)} />;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Admin Panel</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h2 className="font-semibold mb-2">Page Management</h2>
          <p className="mb-2">Create, edit, or delete pages.</p>
          <Button variant="outline">Go to Page Manager</Button>
        </Card>
        <Card className="p-6">
          <h2 className="font-semibold mb-2">Sidebar Management</h2>
          <p className="mb-2">Modify sidebar items and structure.</p>
          <Button variant="outline">Go to Sidebar Manager</Button>
        </Card>
        <Card className="p-6">
          <h2 className="font-semibold mb-2">Design & Content</h2>
          <p className="mb-2">Edit page layouts and content blocks.</p>
          <Button variant="outline">Go to Design Editor</Button>
        </Card>
      </div>
    </div>
  );
}
