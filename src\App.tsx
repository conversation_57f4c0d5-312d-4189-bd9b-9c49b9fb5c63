import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import AdminPanel from "./pages/AdminPanel";
import PrivatePage from "./pages/PrivatePage";
import { SharedLayout } from "@/components/SharedLayout";

// Import pages
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

// Company pages
import Shareholders from "./pages/company/Shareholders";
import VisionMission from "./pages/company/VisionMission";
import CoreValues from "./pages/company/CoreValues";
import SisterCompanies from "./pages/company/SisterCompanies";
import CEO from "./pages/company/CEO";
import ExecutiveChart from "./pages/company/ExecutiveChart";

// Destination pages
import Location from "./pages/destination/Location";
import Residentials from "./pages/destination/Residentials";
import Hotels from "./pages/destination/Hotels";
import Sports from "./pages/destination/Sports";
import Golf from "./pages/destination/sports/Golf";
import Tennis from "./pages/destination/sports/Tennis";
import Clubhouse from "./pages/destination/sports/Clubhouse";
import Sustainability from "./pages/destination/Sustainability";
import ReadAll from "./pages/destination/ReadAll";

// Benefits pages
import Education from "./pages/benefits/Education";
import Insurance from "./pages/benefits/Insurance";
import Accommodation from "./pages/benefits/Accommodation";
import Commuting from "./pages/benefits/Commuting";
import BenefitsSports from "./pages/benefits/Sports";
import Homeowners from "./pages/benefits/Homeowners";

// Policies pages
import TalentManagement from "./pages/policies/TalentManagement";
import Attendance from "./pages/policies/Attendance";

// Team and contacts
import HR from "./pages/team/HR";
import Contacts from "./pages/Contacts";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          
          {/* Company Routes */}
          <Route path="/company/shareholders" element={<SharedLayout><Shareholders /></SharedLayout>} />
          <Route path="/company/vision-mission" element={<SharedLayout><VisionMission /></SharedLayout>} />
          <Route path="/company/core-values" element={<SharedLayout><CoreValues /></SharedLayout>} />
          <Route path="/company/sister-companies" element={<SharedLayout><SisterCompanies /></SharedLayout>} />
          <Route path="/company/ceo" element={<SharedLayout><CEO /></SharedLayout>} />
          <Route path="/company/executive-chart" element={<SharedLayout><ExecutiveChart /></SharedLayout>} />
          <Route path="/admin" element={<AdminPanel />} />
          <Route path="/private" element={<PrivatePage />} />
          
          {/* Destination Routes */}
          <Route path="/destination/location" element={<SharedLayout><Location /></SharedLayout>} />
          <Route path="/destination/residentials" element={<SharedLayout><Residentials /></SharedLayout>} />
          <Route path="/destination/hotels" element={<SharedLayout><Hotels /></SharedLayout>} />
          <Route path="/destination/sports" element={<SharedLayout><Sports /></SharedLayout>} />
          <Route path="/destination/sports/golf" element={<SharedLayout><Golf /></SharedLayout>} />
          <Route path="/destination/sports/tennis" element={<SharedLayout><Tennis /></SharedLayout>} />
          <Route path="/destination/sports/clubhouse" element={<SharedLayout><Clubhouse /></SharedLayout>} />
          <Route path="/destination/sustainability" element={<SharedLayout><Sustainability /></SharedLayout>} />
          <Route path="/destination/read-all" element={<SharedLayout><ReadAll /></SharedLayout>} />
          
          {/* Benefits Routes */}
          <Route path="/benefits/education" element={<SharedLayout><Education /></SharedLayout>} />
          <Route path="/benefits/insurance" element={<SharedLayout><Insurance /></SharedLayout>} />
          <Route path="/benefits/accommodation" element={<SharedLayout><Accommodation /></SharedLayout>} />
          <Route path="/benefits/commuting" element={<SharedLayout><Commuting /></SharedLayout>} />
          <Route path="/benefits/sports" element={<SharedLayout><BenefitsSports /></SharedLayout>} />
          <Route path="/benefits/homeowners" element={<SharedLayout><Homeowners /></SharedLayout>} />
          
          {/* Policies Routes */}
          <Route path="/policies/talent-management" element={<SharedLayout><TalentManagement /></SharedLayout>} />
          <Route path="/policies/attendance" element={<SharedLayout><Attendance /></SharedLayout>} />
          
          {/* Team and Contacts */}
          <Route path="/team/hr" element={<SharedLayout><HR /></SharedLayout>} />
          <Route path="/contacts" element={<SharedLayout><Contacts /></SharedLayout>} />
          
          {/* Legacy routes for backward compatibility */}
          <Route path="/employment" element={<SharedLayout><TalentManagement /></SharedLayout>} />
          <Route path="/attendance" element={<SharedLayout><Attendance /></SharedLayout>} />
          <Route path="/benefits" element={<SharedLayout><Education /></SharedLayout>} />
          <Route path="/policies" element={<SharedLayout><TalentManagement /></SharedLayout>} />
          <Route path="/disciplinary" element={<SharedLayout><TalentManagement /></SharedLayout>} />
          <Route path="/forms" element={<SharedLayout><Contacts /></SharedLayout>} />
          <Route path="/guidelines" element={<SharedLayout><TalentManagement /></SharedLayout>} />
          
          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
