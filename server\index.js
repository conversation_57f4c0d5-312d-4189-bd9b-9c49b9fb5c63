import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import * as jsondb from './jsondb.js';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import path from 'path';

const app = express();
// Using file-based JSON database
const SECRET = 'supersecretkey';

app.use(cors());
app.use(bodyParser.json());

// No-op: JSON DB auto-creates structure

// Auth middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  if (!token) return res.sendStatus(401);
  jwt.verify(token, SECRET, (err, user) => {
    if (err) return res.sendStatus(403);
    req.user = user;
    next();
  });
}

// Register
app.post('/api/register', (req, res) => {
  const { username, password, role } = req.body;
  if (!username || !password) return res.status(400).json({ error: 'Missing fields' });
  const users = jsondb.getUsers();
  if (users.find(u => u.username === username)) return res.status(400).json({ error: 'User already exists' });
  const hash = bcrypt.hashSync(password, 10);
  jsondb.addUser({ id: Date.now(), username, password: hash, role: role || 'member' });
  res.json({ success: true });
});

// Login
app.post('/api/login', (req, res) => {
  const { username, password } = req.body;
  const user = jsondb.getUsers().find(u => u.username === username);
  if (!user) return res.status(401).json({ error: 'Invalid credentials' });
  if (!bcrypt.compareSync(password, user.password)) return res.status(401).json({ error: 'Invalid credentials' });
  const token = jwt.sign({ username: user.username, role: user.role }, SECRET, { expiresIn: '1d' });
  res.json({ token, user: { username: user.username, role: user.role } });
});

// Get all pages (public)
app.get('/api/pages', (req, res) => {
  const pages = jsondb.getPages().map(({ id, title, slug, is_private }) => ({ id, title, slug, is_private }));
  res.json(pages);
});

// Get page by slug (private if needed)
app.get('/api/pages/:slug', (req, res) => {
  const page = jsondb.getPages().find(p => p.slug === req.params.slug);
  if (!page) return res.status(404).json({ error: 'Not found' });
  if (page.is_private) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) return res.status(401).json({ error: 'Unauthorized' });
    try {
      jwt.verify(token, SECRET);
    } catch {
      return res.status(403).json({ error: 'Forbidden' });
    }
  }
  res.json(page);
});

// Create or update page (admin only)
app.post('/api/pages', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') return res.status(403).json({ error: 'Forbidden' });
  const { id, title, slug, content, is_private } = req.body;
  if (!title || !slug || !content) return res.status(400).json({ error: 'Missing fields' });
  if (id) {
    const updated = jsondb.updatePage(id, { title, slug, content, is_private });
    if (!updated) return res.status(404).json({ error: 'Page not found' });
    res.json({ success: true, updated: true });
  } else {
    jsondb.addPage({ id: Date.now(), title, slug, content, is_private: !!is_private });
    res.json({ success: true, created: true });
  }
});

// Delete page (admin only)
app.delete('/api/pages/:id', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') return res.status(403).json({ error: 'Forbidden' });
  jsondb.deletePage(Number(req.params.id));
  res.json({ success: true });
});

// List users (admin only)
app.get('/api/users', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') return res.status(403).json({ error: 'Forbidden' });
  const users = jsondb.getUsers().map(({ id, username, role }) => ({ id, username, role }));
  res.json(users);
});

// Start server
const PORT = process.env.PORT || 4000;
app.listen(PORT, () => {
  console.log(`Policy Guide Hub server running on port ${PORT}`);
});
