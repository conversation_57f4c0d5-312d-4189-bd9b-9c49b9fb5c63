import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Home, Bed, Utensils } from "lucide-react";

const Accommodation = () => {
  const breadcrumbs = [
    { label: "Our Benefits", href: "/benefits" },
    { label: "Accommodation" }
  ];

  return (
    <PageLayout 
      title="Accommodation Benefits" 
      description="Comprehensive accommodation services including lodging, meals, and laundry services."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <ContentSection title="Lodging" id="lodging">
          <div className="text-center">
            <Bed className="h-12 w-12 text-corporate-navy mx-auto mb-4" />
            <p className="text-corporate-gray">Quality housing accommodations for employees and their families.</p>
          </div>
        </ContentSection>

        <ContentSection title="Meals" id="meals">
          <div className="text-center">
            <Utensils className="h-12 w-12 text-corporate-navy mx-auto mb-4" />
            <p className="text-corporate-gray">Meal plans and dining facilities available for all employees.</p>
          </div>
        </ContentSection>

        <ContentSection title="Laundry" id="laundry">
          <div className="text-center">
            <Home className="h-12 w-12 text-corporate-navy mx-auto mb-4" />
            <p className="text-corporate-gray">Professional laundry and cleaning services provided.</p>
          </div>
        </ContentSection>
      </div>
    </PageLayout>
  );
};

export default Accommodation;