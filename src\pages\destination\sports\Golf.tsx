import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Target, Award, Users, MapPin } from "lucide-react";

const Golf = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Sports", href: "/destination/sports" },
    { label: "Golf" }
  ];

  return (
    <PageLayout 
      title="Championship Golf Course" 
      description="Experience world-class golf on our Gary Player-designed 18-hole championship course."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Course Details">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Target className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">18-Hole Championship Course</h4>
                <p className="text-corporate-gray">
                  Designed by legendary golfer <PERSON>, featuring challenging holes with stunning Red Sea views and desert landscapes.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <MapPin className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Strategic Layout</h4>
                <p className="text-corporate-gray">
                  Each hole is strategically designed to challenge golfers of all skill levels while providing breathtaking panoramic views.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Course Statistics">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">7,126</h4>
                <p className="text-sm text-corporate-gray">Yards (Championship Tees)</p>
              </div>
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">Par 72</h4>
                <p className="text-sm text-corporate-gray">Course Rating</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">4</h4>
                <p className="text-sm text-corporate-gray">Tee Options</p>
              </div>
              <div className="text-center p-4 rounded-lg bg-corporate-light">
                <h4 className="text-2xl font-bold text-corporate-navy">18</h4>
                <p className="text-sm text-corporate-gray">Holes</p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Golf Facilities">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <Target className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Driving Range</h4>
            <p className="text-corporate-gray text-sm">300-yard driving range with covered bays and target greens.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Golf Academy</h4>
            <p className="text-corporate-gray text-sm">Professional instruction with PGA-certified golf professionals.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Award className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Pro Shop</h4>
            <p className="text-corporate-gray text-sm">Full-service pro shop with equipment, apparel, and accessories.</p>
          </div>
        </div>
      </ContentSection>

      <ContentSection title="Membership & Packages">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 rounded-lg bg-gradient-primary text-white">
            <h4 className="text-xl font-semibold mb-4">Golf Membership</h4>
            <ul className="space-y-2 text-white/90">
              <li>• Unlimited course access</li>
              <li>• Priority tee time booking</li>
              <li>• Clubhouse privileges</li>
              <li>• Member tournaments</li>
              <li>• Guest privileges</li>
            </ul>
          </div>
          
          <div className="p-6 rounded-lg border border-border">
            <h4 className="text-xl font-semibold text-corporate-navy mb-4">Day Passes</h4>
            <ul className="space-y-2 text-corporate-gray">
              <li>• 18-hole green fees</li>
              <li>• Cart rental included</li>
              <li>• Range access</li>
              <li>• Clubhouse facilities</li>
              <li>• Equipment rental available</li>
            </ul>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default Golf;