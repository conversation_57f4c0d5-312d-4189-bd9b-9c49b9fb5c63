import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Building, Calendar, Globe, TrendingUp } from "lucide-react";

const Shareholders = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "Shareholders" }
  ];

  return (
    <PageLayout 
      title="Shareholders" 
      description="Learn about our founding companies and their rich history in the region."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection 
          title="The Olayan Group"
          description="Founded in 1947, a private multinational enterprise"
        >
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Calendar className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Established 1947</h4>
                <p className="text-corporate-gray">
                  The Olayan Group is a private, multinational enterprise with diverse commercial and industrial operations in the Middle East and an actively managed portfolio of international investments.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Building className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Commercial Operations</h4>
                <p className="text-corporate-gray">
                  The commercial side of the Group comprises more than 40 companies and is centered in Saudi Arabia, where the Group originated. They are engaged in distribution, manufacturing, and services.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Globe className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Global Presence</h4>
                <p className="text-corporate-gray">
                  With offices in Saudi Arabia, Europe, and the US, the Group's global investment team focuses on public and private equities, real estate, fixed income securities, and other specialized assets.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection 
          title="Tawfiq Gargour & Fils"
          description="A legacy spanning nearly a century since 1928"
        >
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Calendar className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Founded 1928</h4>
                <p className="text-corporate-gray">
                  Tawfiq Gargour & Fils (TGF) was established in 1928 in Jaffa, Palestine by the late Tawfiq Gargour joined later by his four sons: Nicolas, Habib, John, and Allenby.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Early Trading</h4>
                <p className="text-corporate-gray">
                  In the early beginning, TGF concentrated on trading activities and started trading in oranges. TGF also traded with the largest companies, such as Salzgitter, and in steel, foodstuffs, and other products.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Building className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Mercedes-Benz Partnership</h4>
                <p className="text-corporate-gray">
                  TGF began establishing its relations with Mercedes-Benz in 1936 when the company contacted them in Germany and brought a few cars in return for some shiploads of oranges.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Globe className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Multi-Million Dollar Business</h4>
                <p className="text-corporate-gray">
                  Over the years TGF has grown into a multi-million-dollar business. By the 1960s, TGF had shipping and trading companies in over 15 countries in the Middle East and Europe, and as far as Canada.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>
      </div>
    </PageLayout>
  );
};

export default Shareholders;