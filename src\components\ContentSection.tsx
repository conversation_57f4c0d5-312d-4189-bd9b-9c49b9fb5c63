import { ReactNode } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface ContentSectionProps {
  title: string;
  description?: string;
  children: ReactNode;
  className?: string;
  id?: string;
}

export function ContentSection({ title, description, children, className = "", id }: ContentSectionProps) {
  return (
    <Card className={`shadow-card ${className}`} id={id}>
      <CardHeader>
        <CardTitle className="text-xl text-corporate-navy">{title}</CardTitle>
        {description && (
          <CardDescription className="text-corporate-gray">
            {description}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );
}