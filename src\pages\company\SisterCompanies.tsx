import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Building, Calendar, Users } from "lucide-react";

const SisterCompanies = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "Sister Companies" }
  ];

  const companies = [
    {
      name: "SBL Somabay Limousine",
      established: "2008",
      description: "Premium transportation services providing luxury limousine and chauffeur services for guests and residents."
    },
    {
      name: "SBT Somabay Travel",
      established: "2023",
      description: "Comprehensive travel services including tour planning, excursions, and travel arrangements for visitors and residents."
    },
    {
      name: "SBTFM Somabay Touristic Facility Management",
      established: "2016",
      description: "Specialized facility management services for touristic properties, ensuring optimal maintenance and operations."
    },
    {
      name: "SBHM Somabay Hospitality Management",
      established: "2021",
      description: "Professional hospitality management services overseeing hotel operations, guest services, and accommodation standards."
    },
    {
      name: "SBFM Somabay Facility Management",
      established: "2022",
      description: "Comprehensive facility management services for residential and commercial properties within the development."
    }
  ];

  return (
    <PageLayout 
      title="Sister Companies" 
      description="Our family of companies working together to deliver exceptional experiences."
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-6">
        {companies.map((company) => (
          <ContentSection key={company.name} title={company.name}>
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="flex items-center gap-4 md:w-1/3">
                <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                  <Building className="h-6 w-6 text-corporate-navy" />
                </div>
                <div>
                  <div className="flex items-center gap-2 text-sm text-corporate-gray">
                    <Calendar className="h-4 w-4" />
                    <span>Established {company.established}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex-1">
                <p className="text-corporate-gray leading-relaxed">
                  {company.description}
                </p>
              </div>
            </div>
          </ContentSection>
        ))}
      </div>

      <ContentSection 
        title="Collaborative Excellence"
        description="How our sister companies work together"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-3">Integrated Services</h4>
            <p className="text-corporate-gray">
              Our companies work seamlessly together to provide comprehensive, integrated solutions.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <Building className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-3">Shared Standards</h4>
            <p className="text-corporate-gray">
              All sister companies maintain the same high standards of quality and service excellence.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg bg-corporate-light">
            <Calendar className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-3">Strategic Growth</h4>
            <p className="text-corporate-gray">
              Each company was established strategically to support our expanding service portfolio.
            </p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default SisterCompanies;