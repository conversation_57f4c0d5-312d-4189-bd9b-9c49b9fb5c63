import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Hotel, Star, Users, Utensils } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

const Hotels = () => {
  const breadcrumbs = [
    { label: "Our Destination", href: "/destination" },
    { label: "Hotels" }
  ];

  return (
    <PageLayout 
      title="Hotel Accommodations" 
      description="World-class hospitality with stunning Red Sea views and exceptional service."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <ContentSection title="Luxury Hotels">
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Hotel className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Five-Star Resort</h4>
                <p className="text-corporate-gray">
                  Premium beachfront resort offering elegant suites, private beaches, and world-class amenities for the ultimate luxury experience.
                </p>
              </div>
            </div>

            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-lg bg-corporate-light flex items-center justify-center">
                <Star className="h-6 w-6 text-corporate-navy" />
              </div>
              <div>
                <h4 className="font-semibold text-corporate-navy mb-2">Boutique Hotel</h4>
                <p className="text-corporate-gray">
                  Intimate and stylish accommodations with personalized service, perfect for couples and discerning travelers.
                </p>
              </div>
            </div>
          </div>
        </ContentSection>

        <ContentSection title="Family-Friendly Options">
          <div className="space-y-4">
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Family Suites</h4>
              <p className="text-corporate-gray">Spacious suites with connecting rooms and family-oriented amenities.</p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Kids Club</h4>
              <p className="text-corporate-gray">Supervised activities and entertainment for children of all ages.</p>
            </div>
            
            <div className="p-4 rounded-lg bg-corporate-light">
              <h4 className="font-semibold text-corporate-navy mb-2">Family Pools</h4>
              <p className="text-corporate-gray">Multiple pools including shallow areas safe for young children.</p>
            </div>
          </div>
        </ContentSection>
      </div>

      <ContentSection title="Hotel Amenities">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <Utensils className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Dining Options</h4>
            <p className="text-corporate-gray text-sm">Multiple restaurants offering international and local cuisine.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Users className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Spa & Wellness</h4>
            <p className="text-corporate-gray text-sm">Full-service spa with massage, beauty treatments, and wellness programs.</p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <Hotel className="h-8 w-8 text-corporate-navy mx-auto mb-4" />
            <h4 className="font-semibold text-corporate-navy mb-2">Business Center</h4>
            <p className="text-corporate-gray text-sm">Modern business facilities and meeting rooms for corporate guests.</p>
          </div>
        </div>
      </ContentSection>
      <div className="text-center mt-8">
        <Button asChild>
          <Link to="/destination/read-all">Read All</Link>
        </Button>
      </div>
    </PageLayout>
  );
};

export default Hotels;
