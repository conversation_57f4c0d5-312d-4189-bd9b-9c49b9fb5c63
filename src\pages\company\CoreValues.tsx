import { PageLayout } from "@/components/PageLayout";
import { ContentSection } from "@/components/ContentSection";
import { Lightbulb, Award, UserCheck, HeartHandshake, RefreshCw, Leaf } from "lucide-react";

const CoreValues = () => {
  const breadcrumbs = [
    { label: "Our Company", href: "/company" },
    { label: "Core Values" }
  ];

  const values = [
    {
      title: "Innovation",
      description: "We embrace creativity and forward-thinking solutions to continuously elevate our offerings and experiences.",
      icon: Lightbulb,
      color: "bg-blue-50 text-blue-600"
    },
    {
      title: "Service Excellence",
      description: "We are committed to delivering personalized, world-class service that exceeds expectations at every touchpoint.",
      icon: Award,
      color: "bg-purple-50 text-purple-600"
    },
    {
      title: "Ownership",
      description: "We take responsibility for our actions and outcomes, ensuring consistency, reliability, and integrity in everything we do.",
      icon: UserCheck,
      color: "bg-green-50 text-green-600"
    },
    {
      title: "Meaningful Connections",
      description: "We build genuine relationships that foster trust, belonging, and a sense of community for both residents and visitors.",
      icon: HeartHandshake,
      color: "bg-red-50 text-red-600"
    },
    {
      title: "Adaptability",
      description: "We remain agile and resilient, embracing change and turning challenges into opportunities for growth.",
      icon: RefreshCw,
      color: "bg-orange-50 text-orange-600"
    },
    {
      title: "Sustainability",
      description: "We champion environmentally responsible practices to preserve our natural surroundings and ensure long-term prosperity.",
      icon: Leaf,
      color: "bg-emerald-50 text-emerald-600"
    }
  ];

  return (
    <PageLayout 
      title="Core Values" 
      description="The fundamental principles that guide our decisions and shape our culture."
      breadcrumbs={breadcrumbs}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {values.map((value, index) => (
          <ContentSection key={value.title} title={value.title}>
            <div className="flex items-start gap-4">
              <div className={`w-12 h-12 rounded-lg ${value.color} flex items-center justify-center shrink-0`}>
                <value.icon className="h-6 w-6" />
              </div>
              <div>
                <p className="text-corporate-gray leading-relaxed">
                  {value.description}
                </p>
              </div>
            </div>
          </ContentSection>
        ))}
      </div>

      <ContentSection 
        title="Living Our Values"
        description="How these values translate into our daily work culture"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">In Decision Making</h4>
            <p className="text-corporate-gray">
              Every decision is evaluated against our core values to ensure alignment with our principles.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">In Performance Reviews</h4>
            <p className="text-corporate-gray">
              Performance is measured not just on results, but on how those results were achieved relative to our values.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-lg border border-border">
            <h4 className="font-semibold text-corporate-navy mb-3">In Recognition</h4>
            <p className="text-corporate-gray">
              We celebrate and recognize team members who exemplify our values in their daily work.
            </p>
          </div>
        </div>
      </ContentSection>
    </PageLayout>
  );
};

export default CoreValues;